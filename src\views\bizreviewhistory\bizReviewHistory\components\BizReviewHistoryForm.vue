<template>
  <el-dialog
    v-model="visible"
    :title="`${paramsProps.title}`"
    :destroy-on-close="true"
    width="800px"
    draggable
  >
    <el-form
      ref="ruleFormRef"
      label-width="140px"
      label-suffix=" :"
      :rules="rules"
      :model="paramsProps.row"
      @submit.enter.prevent="handleSubmit"
    >
      <el-form-item label="内容类型" prop="pubContentType">
        <el-select
          v-model="paramsProps.row.pubContentType"
          clearable
          placeholder="请选择内容类型"
          :disabled="isReadonly"
        >
          <el-option
            v-for="item in useDictOptions('pub_content_type').value"
            :key="item.id"
            :label="item.codeName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <!-- 第一行: 标题 -->
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="paramsProps.row.title"
          placeholder="请填写标题"
          clearable
          :readonly="isReadonly"
        ></el-input>
      </el-form-item>

      <!-- 第二行: 内容，使用富文本编辑器 -->
      <el-form-item label="内容" prop="pubContent">
        <WangEditor
          v-model="paramsProps.row.pubContent"
          height="300px"
          placeholder="请输入内容..."
          :disabled="isReadonly"
        />
      </el-form-item>

      <!-- 第三行: 发布人和联系方式 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="发布人" prop="publisher">
            <el-input
              v-model="paramsProps.row.publisher"
              placeholder="发布人"
              readonly
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系方式" prop="phoneNumber">
            <el-input
              v-model="paramsProps.row.phoneNumber"
              placeholder="请填写联系方式"
              clearable
              :readonly="isReadonly"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第四行: 附件列表 -->
      <el-form-item label="附件列表" prop="fileList">
        <UploadFiles
          v-model:file-list="fileList"
          multiple
          :limit="10"
          :file-size="10"
          width="100%"
          height="120px"
          :disabled="isReadonly"
          @change="fileChange"
        >
          <template #tip> 支持多个文件上传，单个文件大小不能超过 10M </template>
        </UploadFiles>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false"> 取消</el-button>
      <template v-if="!isReadonly">
        <el-button type="info" @click="handleSubmit('2001001')"> 保存为草稿</el-button>
        <el-button type="primary" @click="handleSubmit('2001002')"> 申请发布</el-button>
      </template>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { type ElForm, ElMessage, type UploadUserFile } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import WangEditor from '@/components/Editor/index.vue'
import UploadFiles from '@/components/Upload/file.vue'
import type { INewUploadResult } from '@/api/types/system/upload'
import { useDictOptions } from '@/hooks/useDictOptions';

defineOptions({
    name: 'BizPubContentForm'
})

const userStore = useUserStore()
const rules = reactive({
  pubContentType: [{ required: true, message: '请选择内容类型', trigger: 'blur' }],
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  pubContent: [{ required: true, message: '请输入内容', trigger: 'blur' }],
  phoneNumber: [{ required: true, message: '请输入联系方式', trigger: 'blur' }]
})

const visible = ref(false)
const paramsProps = ref<View.DefaultParams>({
  title: '',
  row: {},
  api: undefined,
  getTableList: undefined
})

// 计算是否为只读模式
const isReadonly = computed(() => {
  return paramsProps.value.isReadonly === true
})

// 文件列表相关
const fileList = ref<UploadUserFile[]>([])

// 提取文件ID的辅助函数
const extractFileIds = (files: UploadUserFile[]): string[] => {
  return files
    .filter(file => {
      // 只保留有真实文件ID的文件（上传成功的文件）
      const response = file.response as INewUploadResult
      return response?.id || (file.status === 'success' && file.url && !file.url.startsWith('blob:'))
    })
    .map(file => {
      const response = file.response as INewUploadResult
      // 优先使用响应中的ID，如果没有则尝试从URL中提取fileId参数
      if (response?.id) {
        return response.id
      }
      // 对于编辑模式下的已有文件，从URL中提取fileId
      if (file.url && file.url.includes('fileId=')) {
        const match = file.url.match(/fileId=([^&]+)/)
        return match ? match[1] : null
      }
      return null
    })
    .filter(id => id) as string[] // 过滤掉null值并断言为string[]
}

// 更新fileList字段的函数
const updateFileListField = () => {
  const fileIds = extractFileIds(fileList.value)
  const fileListString = fileIds.join(',')

  console.log('更新fileList字段:', {
    fileIds,
    fileListString,
    currentFileList: fileList.value.map(f => ({
      name: f.name,
      status: f.status,
      response: f.response,
      uid: f.uid,
      url: f.url
    }))
  })

  paramsProps.value.row.fileList = fileListString
}

// 文件上传变化处理
const fileChange = (fileInfo: INewUploadResult) => {
  console.log('文件上传变化:', fileInfo)

  // 当文件上传成功或删除时，更新fileList字段
  updateFileListField()
}

// 接收父组件传过来的参数
const acceptParams = (params: View.DefaultParams) => {
  paramsProps.value = params

  // 设置发布人为当前登录用户的nickname
  if (!paramsProps.value.row.publisher) {
    paramsProps.value.row.publisher = userStore.userInfo.nickname || userStore.userInfo.username
  }

  // 处理文件列表回显
  if (paramsProps.value.row.fileListInfo && Array.isArray(paramsProps.value.row.fileListInfo)) {
    fileList.value = paramsProps.value.row.fileListInfo.map((file: any, index: number) => ({
      uid: index,
      name: file.filename || file.name || '未知文件',
      url: file.url,
      status: 'success'
    }))
  } else {
    fileList.value = []
  }

  visible.value = true
}

// 监听文件列表变化，更新fileList字段
watch(fileList, () => {
  updateFileListField()
}, { deep: true })

// 提交数据（新增/编辑）
const ruleFormRef = ref<InstanceType<typeof ElForm>>()
const handleSubmit = (reviewStatus?: string) => {
  ruleFormRef.value!.validate(async (valid) => {
    if (!valid) return
    try {
      // 设置审核状态
      if (reviewStatus) {
        paramsProps.value.row.reviewStatus = reviewStatus
      }

      await paramsProps.value.api!(paramsProps.value.row)

      const actionText = reviewStatus === '2001001' ? '保存草稿' : reviewStatus === '2001002' ? '申请发布' : paramsProps.value.title
      ElMessage.success({ message: `${actionText}成功！` })
      paramsProps.value.getTableList!()
      visible.value = false
    } catch (error) {
      console.log(error)
    }
  })
}

defineExpose({
  acceptParams
})
</script>

<style scoped lang="scss"></style>