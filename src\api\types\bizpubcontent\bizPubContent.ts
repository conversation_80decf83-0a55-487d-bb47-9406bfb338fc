import type { IPageQuery } from '@/api/types'

// 查询条件
export type BizPubContentQuery = IPageQuery & {
    reviewStatus?: string
    pubContent?: string
    createTime?: string
    phoneNumber?: string
    fileList?: string
    title?: string
    publisher?: string
    pubContentType?: string
    action?: string
  }

// 编辑form表单
export type BizPubContentForm = {
    pubId?: string
    reviewStatus?: string
    pubContent?: string
    phoneNumber?: string
    fileList?: string
    title?: string
    publisher?: string
    fileListInfo?: Array<{filename: string, url: string}>
    pubContentType?: string
    action?: string
    reviewHistoryList: Array<{"createUser":string,"createTime":string,"auditorUser":string,"reviewStatus":string,"reviewContent":string}>

 }

// list或detail返回结构
export type BizPubContentRow = {
    pubId?: string
    reviewStatus?: string
    pubContent?: string
    createId?: number
    createTime?: string
    updateTime?: string
    phoneNumber?: string
    fileList?: string
    title?: string
    publisher?: string
    fileListInfo?: Array<{filename: string, url: string}>
    pubContentType?: string
    reviewHistoryList: Array<{"createUser":string,"createTime":string,"auditorUser":string,"reviewStatus":string,"reviewContent":string}>
  }

