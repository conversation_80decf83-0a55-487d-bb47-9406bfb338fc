<template>
  <div class="test-editor-page">
    <h2>富文本编辑器测试页面</h2>
    
    <div class="editor-demo">
      <h3>基础编辑器</h3>
      <WangEditor
        v-model="content1"
        placeholder="请输入内容..."
        height="300px"
        upload-dir="test"
        @change="handleChange"
      />
      
      <div class="content-preview">
        <h4>内容预览：</h4>
        <div v-html="content1" class="preview-content"></div>
      </div>
    </div>

    <div class="editor-demo">
      <h3>简单模式编辑器</h3>
      <WangEditor
        v-model="content2"
        placeholder="简单模式编辑器..."
        height="200px"
        mode="simple"
        upload-dir="test"
      />
      
      <div class="content-preview">
        <h4>内容预览：</h4>
        <div v-html="content2" class="preview-content"></div>
      </div>
    </div>

    <div class="form-demo">
      <h3>表单集成测试</h3>
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        
        <el-form-item label="内容" prop="content">
          <WangEditor
            v-model="form.content"
            placeholder="请输入内容..."
            height="300px"
            upload-dir="form-test"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import WangEditor from '@/components/Editor/index.vue'

defineOptions({
  name: 'TestEditor'
})

// 基础编辑器内容
const content1 = ref('<p>这是一个测试内容</p>')
const content2 = ref('')

// 表单数据
const form = reactive({
  title: '',
  content: ''
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入内容', trigger: 'blur' }
  ]
}

const formRef = ref<FormInstance>()

// 内容变化处理
const handleChange = (value: string) => {
  console.log('编辑器内容变化:', value)
}

// 提交表单
const submitForm = () => {
  formRef.value?.validate((valid) => {
    if (valid) {
      console.log('表单数据:', form)
      ElMessage.success('提交成功！')
    } else {
      ElMessage.error('请检查表单内容')
    }
  })
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
}
</script>

<style scoped lang="scss">
.test-editor-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  h2 {
    color: #409eff;
    margin-bottom: 30px;
  }

  .editor-demo {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    h3 {
      margin-bottom: 20px;
      color: #606266;
    }

    .content-preview {
      margin-top: 20px;
      padding: 15px;
      background-color: #f5f7fa;
      border-radius: 4px;

      h4 {
        margin-bottom: 10px;
        color: #909399;
        font-size: 14px;
      }

      .preview-content {
        min-height: 50px;
        padding: 10px;
        background-color: #fff;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
      }
    }
  }

  .form-demo {
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    h3 {
      margin-bottom: 20px;
      color: #606266;
    }
  }
}
</style>
