<template>
  <div class="editor-container" :class="{ disabled: disabled }">
    <!-- 工具栏 -->
    <div ref="toolbarRef" class="editor-toolbar" />
    <!-- 编辑器 -->
    <div ref="editorRef" class="editor-content" :style="{ height: height }" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, inject, nextTick } from 'vue'
import { createEditor, createToolbar } from '@wangeditor/editor'
import type { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor'
import { uploadFile } from '@/api/modules/system/upload'
import { ElMessage, formContextKey, formItemContextKey } from 'element-plus'

defineOptions({
  name: 'WangEditor'
})

interface EditorProps {
  modelValue?: string // 编辑器内容
  height?: string // 编辑器高度
  placeholder?: string // 占位符
  disabled?: boolean // 是否禁用
  mode?: 'default' | 'simple' // 编辑器模式
  toolbarConfig?: Partial<IToolbarConfig> // 工具栏配置
  editorConfig?: Partial<IEditorConfig> // 编辑器配置
  uploadDir?: string // 上传目录
}

const props = withDefaults(defineProps<EditorProps>(), {
  modelValue: '',
  height: '300px',
  placeholder: '请输入内容...',
  disabled: false,
  mode: 'default',
  uploadDir: 'editor'
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  change: [value: string]
}>()

// 表单验证相关
const formContext = inject(formContextKey, undefined)
const formItemContext = inject(formItemContextKey, undefined)

// 编辑器实例
const editorRef = ref<HTMLElement>()
const toolbarRef = ref<HTMLElement>()
let editor: IDomEditor | null = null

// 工具栏配置
const getToolbarConfig = (): Partial<IToolbarConfig> => {
  return {
    excludeKeys: props.mode === 'simple' ? [
      'blockquote',
      'bgColor',
      'fontSize',
      'fontFamily',
      'indent',
      'justify',
      'emotion',
      'insertVideo',
      'insertTable',
      'codeBlock',
      'divider',
      'fullScreen'
    ] : []
  }
}

// 编辑器配置
const getEditorConfig = (): Partial<IEditorConfig> => {
  return {
    placeholder: props.placeholder,
    readOnly: props.disabled,
    MENU_CONF: {
      // 配置上传图片
      uploadImage: {
        async customUpload(file: File, insertFn: (url: string, alt?: string, href?: string) => void) {
          try {
            const { data } = await uploadFile({
              file: file as any,
              dirTag: props.uploadDir
            })

            // 插入图片到编辑器
            insertFn(data.url, file.name, data.url)

            ElMessage.success('图片上传成功')
          } catch (error) {
            console.error('图片上传失败:', error)
            ElMessage.error('图片上传失败')
          }
        }
      }
    }
  }
}

// 初始化编辑器
const initEditor = () => {
  if (!editorRef.value || !toolbarRef.value) {
    console.warn('编辑器容器未准备好')
    return
  }

  try {
    // 合并配置
    const editorConfig = {
      ...getEditorConfig(),
      ...props.editorConfig
    }

    const toolbarConfig = {
      ...getToolbarConfig(),
      ...props.toolbarConfig
    }

    // 创建编辑器
    editor = createEditor({
      selector: editorRef.value,
      config: editorConfig,
      mode: props.mode
    })

    // 创建工具栏
    createToolbar({
      editor,
      selector: toolbarRef.value,
      config: toolbarConfig,
      mode: props.mode
    })

    // 设置初始内容
    if (props.modelValue) {
      editor.setHtml(props.modelValue)
    }

    // 监听内容变化
    editor.on('change', () => {
      const html = editor?.getHtml() || ''
      emit('update:modelValue', html)
      emit('change', html)

      // 触发表单验证
      if (formItemContext?.prop) {
        formContext?.validateField([formItemContext.prop as string])
      }
    })

    console.log('编辑器初始化成功')
  } catch (error) {
    console.error('编辑器初始化失败:', error)
    ElMessage.error('编辑器初始化失败')
  }
}

// 销毁编辑器
const destroyEditor = () => {
  if (editor) {
    editor.destroy()
    editor = null
  }
}

// 监听内容变化
watch(() => props.modelValue, (newVal) => {
  if (editor && newVal !== editor.getHtml()) {
    editor.setHtml(newVal || '')
  }
})

// 监听禁用状态
watch(() => props.disabled, (disabled) => {
  if (editor) {
    if (disabled) {
      editor.disable()
    } else {
      editor.enable()
    }
  }
})

// 暴露方法
const getHtml = () => editor?.getHtml() || ''
const getText = () => editor?.getText() || ''
const setHtml = (html: string) => editor?.setHtml(html)
const insertText = (text: string) => editor?.insertText(text)
const focus = () => editor?.focus()
const blur = () => editor?.blur()

defineExpose({
  getHtml,
  getText,
  setHtml,
  insertText,
  focus,
  blur,
  editor: () => editor
})

onMounted(() => {
  // 使用 nextTick 确保 DOM 完全渲染
  nextTick(() => {
    // 添加小延迟确保元素完全可用
    setTimeout(() => {
      initEditor()
    }, 100)
  })
})

onBeforeUnmount(() => {
  destroyEditor()
})
</script>

<style scoped lang="scss">
.editor-container {
  border: 1px solid #e8eaec;
  border-radius: 4px;

  .editor-toolbar {
    border-bottom: 1px solid #e8eaec;
  }

  .editor-content {
    overflow-y: auto;
  }
}

// 编辑器样式
:deep(.w-e-text-container) {
  background-color: #fff;
}

:deep(.w-e-text-placeholder) {
  color: #c0c4cc;
}

:deep(.w-e-toolbar) {
  background-color: #fafafa;
  border-bottom: 1px solid #e8eaec;
}

:deep(.w-e-toolbar .w-e-bar-item button) {
  color: #606266;

  &:hover {
    color: #409eff;
    background-color: #ecf5ff;
  }
}

:deep(.w-e-text-container) {
  min-height: 200px;
}

// 禁用状态样式
.editor-container.disabled {
  :deep(.w-e-text-container) {
    background-color: #f5f7fa;
    color: #c0c4cc;
    cursor: not-allowed;
  }

  :deep(.w-e-toolbar) {
    background-color: #f5f7fa;

    .w-e-bar-item button {
      color: #c0c4cc;
      cursor: not-allowed;

      &:hover {
        color: #c0c4cc;
        background-color: transparent;
      }
    }
  }
}
</style>