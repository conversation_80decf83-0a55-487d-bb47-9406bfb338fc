<template>
  <div class="table-box">
    <ProTable
      ref="proTableRef"
      title="内容发布表"
      :indent="20"
      :columns="columns"
      :search-columns="searchColumns"
      :request-api="getTableList"
      row-key="pubId"
    >
      
      <template #operation="{ row }">
        <el-button
          v-auth="'biz.pub.content.view'"
          type="primary"
          link
          :icon="View"
          @click="openView('查看内容发布表', row)"
        >
          查看
        </el-button>
        <el-button
          v-auth="'biz.pub.content.remove'"
          type="danger"
          link
          :icon="Delete"
          @click="deleteInfo(row)"
        >
          删除
        </el-button>
        <el-button
          v-auth="'biz.pub.content.reject'" v-if="row.reviewStatus == '2001002'"
          type="warning"
          link
          :icon="Close"
          @click="rejectContent(row)"
        >
          驳回
        </el-button>
        <el-button
          v-auth="'biz.pub.content.agree'"  v-if="row.reviewStatus != '2001003'"
          type="success"
          link
          :icon="Check"
          @click="agreePublish(row)"
        >
          同意发布
        </el-button>
        <el-button
          v-auth="'biz.pub.content.take_down'" v-if="row.isShow == '2000001' && row.reviewStatus == '2001003'"
          type="info"
          link
          :icon="Bottom"
          @click="takeDown(row)"
        >
          下架
        </el-button>
        <el-button
          v-auth="'biz.pub.content.take_up'" v-if="row.isShow == '2000002' && row.reviewStatus == '2001003'"
          type="primary"
          link
          :icon="Top"
          @click="takeUp(row)"
        >
          上架
        </el-button>
        
      </template>
    </ProTable>
    <BizReviewHistoryForm ref="bizReviewHistoryRef" />
    <ImportExcel ref="ImportExcelRef" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  CirclePlus,
  Delete,
  EditPen,
  Upload,
  Download,
  View,
  Close,
  Check,
  Bottom,
  Top,
} from '@element-plus/icons-vue'
import ProTable from '@/components/ProTable/index.vue'
import {
  createBizPubContentApi,
  removeBizPubContentApi,
  updateBizPubContentApi,
  getBizPubContentListApi,
  getBizPubContentDetailApi,
  importBizPubContentExcelApi,
  exportBizPubContentExcelApi,
} from '@/api/modules/bizpubcontentreview/bizPubContentReview';
import { useHandleData } from '@/hooks/useHandleData';
import BizReviewHistoryForm from '@/views/bizreviewhistory/bizReviewHistory/components/BizReviewHistoryForm.vue';
import { useDictOptions } from '@/hooks/useDictOptions';
import type { ColumnProps, ProTableInstance, SearchProps } from '@/components/ProTable/interface';
import type { BizPubContentQuery, BizPubContentRow } from '@/api/types/bizpubcontentreview/bizPubContentReview';
import ImportExcel from '@/components/ImportExcel/index.vue';
import { downloadTemplate } from '@/api/modules/system/common';
import { useDownload } from "@/hooks/useDownload";
import { ElMessage, ElMessageBox } from 'element-plus';
defineOptions({
  name: 'BizReviewHistoryView'
})
const proTableRef = ref<ProTableInstance>();
// 表格配置项
const columns: ColumnProps<BizPubContentRow>[] = [
  { type: 'selection', width: 80 },
  {
    prop: 'pubContentType',
    label: '内容类型',
    tag: true,
    enum: useDictOptions('pub_content_type'),
    fieldNames: {
      label: 'codeName',
      value: 'id',
      tagType: 'callbackShowStyle'
    },
    width: 120
  },
  { prop: 'title', label: '标题', width: 200 },
  { prop: 'reviewStatus',
    label: '审核状态',
    tag: true,
    enum: useDictOptions('review_status'),
    fieldNames: {
      label: "codeName",
      value: "id",
      tagType: "callbackShowStyle"
    },
    width: 120
  },
  { prop: 'isShow',
    label: '是否上架',
    tag: true,
    enum: useDictOptions('is_not'),
    fieldNames: {
      label: "codeName",
      value: "id",
      tagType: "callbackShowStyle"
    },
    width: 120
  },
  { prop: 'isFirst',
    label: '是否置顶',
    tag: true,
    enum: useDictOptions('is_not'),
    fieldNames: {
      label: "codeName",
      value: "id",
      tagType: "callbackShowStyle"
    },
    width: 120
  },

  // { prop: 'pubContent', label: '内容', width: 300, showOverflowTooltip: true },
  { prop: 'publisher', label: '发布人', width: 120 },
  { prop: 'phoneNumber', label: '联系方式', width: 150 },
  { prop: 'createTime', label: '创建时间', width: 180 },
  { prop: 'updateTime', label: '更新时间', width: 180 },
  { prop: 'operation', label: '操作', width: 500, fixed: 'right' }
]
// 搜索条件项
const searchColumns: SearchProps[] = [
  { prop: 'title', label: '标题', el: 'input' },
  {
    prop: 'pubContentType',
    label: '内容类型',
    el: 'select',
    enum: useDictOptions('pub_content_type'),
    fieldNames: {
      label: 'codeName',
      value: 'id',
      tagType: 'callbackShowStyle'
    },
  },
  { prop: 'reviewStatus',
    label: '审核状态',
    el: 'select',
    enum: useDictOptions('review_status'),
    fieldNames: {
      label: "codeName",
      value: "id",
      tagType: "callbackShowStyle"
    },
  },
  { prop: 'publisher', label: '发布人', el: 'input' },
  { prop: 'phoneNumber', label: '联系方式', el: 'input' },
]
// 获取table列表
const getTableList = (params: BizPubContentQuery) => {
  let newParams = formatParams(params);
  return getBizPubContentListApi(newParams);
};
const formatParams = (params: BizPubContentQuery) =>{
  let newParams = JSON.parse(JSON.stringify(params));
  if(newParams.updateTime) {
    newParams.updateTimeStart = newParams.updateTime[0];
    newParams.updateTimeEnd = newParams.updateTime[1];
    delete newParams.updateTime;
  }

  return newParams;
}
// 打开 drawer(新增、查看、编辑)
const bizReviewHistoryRef = ref<InstanceType<typeof BizReviewHistoryForm>>()
const openAddEdit = async(title: string, row: any = {}, isAdd = true) => {
  if (!isAdd) {
    const record = await getBizPubContentDetailApi({ id: row?.pubId })
    row = record?.data
  }
  const params = {
    title,
    row: { ...row },
    api: isAdd ? createBizPubContentApi : updateBizPubContentApi,
    getTableList: proTableRef.value?.getTableList
  }
  bizReviewHistoryRef.value?.acceptParams(params)
}

// 查看内容（只读模式）
const openView = async(title: string, row: any) => {
  const record = await getBizPubContentDetailApi({ id: row?.pubId })
  const params = {
    title,
    row: { ...record?.data },
    api: undefined, // 只读模式不需要API
    getTableList: proTableRef.value?.getTableList,
    isReadonly: true // 标记为只读模式
  }
  bizReviewHistoryRef.value?.acceptParams(params)
}
// 删除信息
const deleteInfo = async (params: BizPubContentRow) => {
  await useHandleData(
    updateBizPubContentApi,
    { pubId: params.pubId, action: 'delete' },
    `删除【${params.title}】内容发布表`
  )
  proTableRef.value?.getTableList()
}

// 驳回内容
const rejectContent = async (row: BizPubContentRow) => {
  try {
    const { value: remark } = await ElMessageBox.prompt('请输入审核意见', '驳回审核', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputType: 'textarea',
      inputPlaceholder: '请填写驳回原因...',
      inputValidator: (value) => {
        if (!value || value.trim() === '') {
          return '请输入驳回原因'
        }
        return true
      }
    })

    await updateBizPubContentApi({
      pubId: row.pubId,
      action: 'reject',
      reviewContent: remark.trim()
    })

    ElMessage.success('驳回成功！')
    proTableRef.value?.getTableList()
  } catch (error) {
    // 用户取消操作，不显示错误信息
  }
}

// 同意发布
const agreePublish = async (row: BizPubContentRow) => {
  await useHandleData(
    updateBizPubContentApi,
    { pubId: row.pubId, action: 'agree' },
    `同意发布【${row.title}】`
  )
  proTableRef.value?.getTableList()
}

// 下架
const takeDown = async (row: BizPubContentRow) => {
  await useHandleData(
    updateBizPubContentApi,
    { pubId: row.pubId, action: 'take_down' },
    `下架【${row.title}】`
  )
  proTableRef.value?.getTableList()
}

// 上架
const takeUp = async (row: BizPubContentRow) => {
  await useHandleData(
    updateBizPubContentApi,
    { pubId: row.pubId, action: 'take_up' },
    `上架【${row.title}】`
  )
  proTableRef.value?.getTableList()
}
// 批量删除信息
const batchDelete = async (ids: (string | number)[]) => {
  await useHandleData(removeBizPubContentApi, { ids }, '删除所选内容发布表')
  proTableRef.value?.clearSelection()
  proTableRef.value?.getTableList()
}
// 导入
const ImportExcelRef = ref<InstanceType<typeof ImportExcel>>()
const importData = () => {
  const params = {
    title: '内容发布表',
    templateName: '内容发布表',
    tempApi: downloadTemplate,
    importApi: importBizPubContentExcelApi,
    getTableList: proTableRef.value?.getTableList
  }
  ImportExcelRef.value?.acceptParams(params)
}
// 导出
const downloadFile = async () => {
  let newParams = formatParams(proTableRef.value?.searchParam as BizPubContentQuery);
  useDownload(exportBizPubContentExcelApi, "内容发布表", newParams);
};
</script>