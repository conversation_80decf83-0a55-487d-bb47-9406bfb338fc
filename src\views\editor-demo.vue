<template>
  <div class="editor-demo-page">
    <div class="demo-header">
      <h1>富文本编辑器演示</h1>
      <p>基于 wangEditor v5 封装的 Vue 3 富文本编辑器组件</p>
    </div>

    <!-- 基础演示 -->
    <div class="demo-section">
      <h2>1. 基础使用</h2>
      <div class="demo-content">
        <WangEditor
          v-model="basicContent"
          placeholder="请输入内容..."
          height="300px"
          upload-dir="demo"
          @change="handleBasicChange"
        />
        <div class="content-display">
          <h4>实时内容预览：</h4>
          <div class="preview-box" v-html="basicContent"></div>
        </div>
      </div>
    </div>

    <!-- 简单模式演示 -->
    <div class="demo-section">
      <h2>2. 简单模式</h2>
      <div class="demo-content">
        <WangEditor
          v-model="simpleContent"
          mode="simple"
          placeholder="简单模式编辑器..."
          height="200px"
          upload-dir="demo-simple"
        />
        <div class="content-display">
          <h4>简单模式内容：</h4>
          <div class="preview-box" v-html="simpleContent"></div>
        </div>
      </div>
    </div>

    <!-- 表单集成演示 -->
    <div class="demo-section">
      <h2>3. 表单集成</h2>
      <div class="demo-content">
        <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
          <el-form-item label="标题" prop="title">
            <el-input v-model="formData.title" placeholder="请输入标题" />
          </el-form-item>
          
          <el-form-item label="内容" prop="content">
            <WangEditor
              v-model="formData.content"
              placeholder="请输入文章内容..."
              height="350px"
              upload-dir="form-demo"
            />
          </el-form-item>
          
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio-button value="draft">草稿</el-radio-button>
              <el-radio-button value="published">已发布</el-radio-button>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="submitForm">提交表单</el-button>
            <el-button @click="resetForm">重置表单</el-button>
            <el-button @click="previewForm">预览数据</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 禁用状态演示 -->
    <div class="demo-section">
      <h2>4. 禁用状态</h2>
      <div class="demo-content">
        <el-switch v-model="isDisabled" active-text="启用" inactive-text="禁用" />
        <div style="margin-top: 15px;">
          <WangEditor
            v-model="disabledContent"
            :disabled="isDisabled"
            placeholder="这是一个可以切换禁用状态的编辑器..."
            height="250px"
          />
        </div>
      </div>
    </div>

    <!-- 方法演示 -->
    <div class="demo-section">
      <h2>5. 组件方法演示</h2>
      <div class="demo-content">
        <div class="method-buttons">
          <el-button @click="getEditorContent">获取内容</el-button>
          <el-button @click="setEditorContent">设置内容</el-button>
          <el-button @click="insertTextToEditor">插入文本</el-button>
          <el-button @click="focusEditor">聚焦编辑器</el-button>
          <el-button @click="clearEditor">清空内容</el-button>
        </div>
        <WangEditor
          ref="methodEditorRef"
          v-model="methodContent"
          placeholder="用于演示方法调用的编辑器..."
          height="250px"
          upload-dir="method-demo"
        />
      </div>
    </div>

    <!-- 数据展示 -->
    <div class="demo-section">
      <h2>6. 数据状态</h2>
      <div class="demo-content">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="基础内容长度">
            {{ basicContent.length }} 字符
          </el-descriptions-item>
          <el-descriptions-item label="简单模式内容">
            {{ simpleContent || '暂无内容' }}
          </el-descriptions-item>
          <el-descriptions-item label="表单标题">
            {{ formData.title || '暂无标题' }}
          </el-descriptions-item>
          <el-descriptions-item label="表单状态">
            {{ formData.status === 'draft' ? '草稿' : '已发布' }}
          </el-descriptions-item>
          <el-descriptions-item label="编辑器状态">
            {{ isDisabled ? '禁用' : '启用' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import WangEditor from '@/components/Editor/index.vue'

defineOptions({
  name: 'EditorDemo'
})

// 基础内容
const basicContent = ref('<p>这是一个功能完整的富文本编辑器</p><p>支持<strong>加粗</strong>、<em>斜体</em>、<u>下划线</u>等格式</p>')

// 简单模式内容
const simpleContent = ref('')

// 表单数据
const formData = reactive({
  title: '',
  content: '<p>请在这里输入文章内容...</p>',
  status: 'draft'
})

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入内容', trigger: 'blur' }
  ]
}

// 禁用状态
const isDisabled = ref(false)
const disabledContent = ref('<p>这个编辑器可以切换禁用状态</p>')

// 方法演示
const methodContent = ref('<p>方法演示编辑器</p>')
const methodEditorRef = ref()
const formRef = ref<FormInstance>()

// 事件处理
const handleBasicChange = (value: string) => {
  console.log('基础编辑器内容变化:', value)
}

// 表单操作
const submitForm = () => {
  formRef.value?.validate((valid) => {
    if (valid) {
      console.log('表单数据:', formData)
      ElMessage.success('表单提交成功！')
    } else {
      ElMessage.error('请检查表单内容')
    }
  })
}

const resetForm = () => {
  formRef.value?.resetFields()
}

const previewForm = () => {
  console.log('表单预览:', formData)
  ElMessage.info('请查看控制台输出')
}

// 方法演示
const getEditorContent = () => {
  if (methodEditorRef.value) {
    const html = methodEditorRef.value.getHtml()
    const text = methodEditorRef.value.getText()
    ElMessage.info(`HTML: ${html.length} 字符, 文本: ${text.length} 字符`)
    console.log('HTML内容:', html)
    console.log('文本内容:', text)
  }
}

const setEditorContent = () => {
  if (methodEditorRef.value) {
    const newContent = '<p>这是通过方法设置的新内容</p><p><strong>当前时间:</strong> ' + new Date().toLocaleString() + '</p>'
    methodEditorRef.value.setHtml(newContent)
    ElMessage.success('内容已设置')
  }
}

const insertTextToEditor = () => {
  if (methodEditorRef.value) {
    methodEditorRef.value.insertText(' [插入的文本] ')
    ElMessage.success('文本已插入')
  }
}

const focusEditor = () => {
  if (methodEditorRef.value) {
    methodEditorRef.value.focus()
    ElMessage.info('编辑器已聚焦')
  }
}

const clearEditor = () => {
  if (methodEditorRef.value) {
    methodEditorRef.value.setHtml('')
    ElMessage.success('内容已清空')
  }
}
</script>

<style scoped lang="scss">
.editor-demo-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #f5f7fa;
  min-height: 100vh;

  .demo-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;

    h1 {
      margin: 0 0 10px 0;
      font-size: 2.5em;
      font-weight: 300;
    }

    p {
      margin: 0;
      font-size: 1.2em;
      opacity: 0.9;
    }
  }

  .demo-section {
    margin-bottom: 40px;
    background: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    h2 {
      margin: 0 0 20px 0;
      color: #409eff;
      font-size: 1.5em;
      border-bottom: 2px solid #e4e7ed;
      padding-bottom: 10px;
    }

    .demo-content {
      .content-display {
        margin-top: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;

        h4 {
          margin: 0 0 10px 0;
          color: #6c757d;
          font-size: 14px;
        }

        .preview-box {
          min-height: 50px;
          padding: 10px;
          background: white;
          border: 1px solid #dee2e6;
          border-radius: 4px;
          word-break: break-word;
        }
      }

      .method-buttons {
        margin-bottom: 15px;
        
        .el-button {
          margin-right: 10px;
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>
