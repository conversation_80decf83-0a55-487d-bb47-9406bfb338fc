import { HOME_URL, LOGIN_URL } from '@/config';
import type { RouteRecordRaw } from 'vue-router';

export const staticRouter: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: HOME_URL
  },
  {
    path: LOGIN_URL,
    name: 'login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录'
    }
  },
  {
    path: '/layout',
    name: 'layout',
    component: () => import('@/layouts/index.vue'),
    redirect: HOME_URL,
    children: [
      {
        path: HOME_URL,
        name: 'home',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '首页',
          icon: 'HomeFilled',
          isAffix: 'T',
          isFull: 'F',
          isHidden: 'F',
          isKeepAlive: 'T',
          isLink: ''
        }
      },
      {
        path: '/test-editor',
        name: 'test-editor',
        component: () => import('@/views/test-editor.vue'),
        meta: {
          title: '富文本编辑器测试',
          icon: 'Edit',
          isAffix: 'F',
          isFull: 'F',
          isHidden: 'F',
          isKeepAlive: 'T',
          isLink: ''
        }
      },
      {
        path: '/minimal-editor-test',
        name: 'minimal-editor-test',
        component: () => import('@/views/minimal-editor-test.vue'),
        meta: {
          title: '最小化编辑器测试',
          icon: 'Edit',
          isAffix: 'F',
          isFull: 'F',
          isHidden: 'F',
          isKeepAlive: 'T',
          isLink: ''
        }
      },
      {
        path: '/editor-demo',
        name: 'editor-demo',
        component: () => import('@/views/editor-demo.vue'),
        meta: {
          title: '富文本编辑器完整演示',
          icon: 'Edit',
          isAffix: 'F',
          isFull: 'F',
          isHidden: 'F',
          isKeepAlive: 'T',
          isLink: ''
        }
      },

    ]
  }
];

/**
 * errorRouter (错误页面路由)
 */
export const errorRouter: RouteRecordRaw[] = [
  {
    path: '/403',
    name: '403',
    component: () => import('@/components/ErrorMessage/403.vue'),
    meta: {
      title: '403页面'
    }
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/components/ErrorMessage/404.vue'),
    meta: {
      title: '404页面'
    }
  },
  {
    path: '/500',
    name: '500',
    component: () => import('@/components/ErrorMessage/500.vue'),
    meta: {
      title: '500页面'
    }
  },
  // Resolve refresh page, route warnings
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/components/ErrorMessage/404.vue')
  }
];
