# 富文本编辑器组件使用说明

## 概述

基于 wangEditor v5 封装的 Vue 3 富文本编辑器组件，支持图片上传、表单验证等功能。

## 安装依赖

```bash
npm install @wangeditor/editor --legacy-peer-deps
```

## 组件特性

- ✅ 基于 wangEditor v5，功能强大
- ✅ 支持图片上传（集成项目现有上传 API）
- ✅ 支持双向数据绑定 (v-model)
- ✅ 支持表单验证
- ✅ 支持简单模式和完整模式
- ✅ 支持禁用状态
- ✅ 支持自定义配置
- ✅ TypeScript 支持

## 基础使用

### 1. 导入组件

```vue
<script setup lang="ts">
import WangEditor from '@/components/Editor/index.vue'
import { ref } from 'vue'

const content = ref('<p>初始内容</p>')
</script>
```

### 2. 模板中使用

```vue
<template>
  <WangEditor
    v-model="content"
    placeholder="请输入内容..."
    height="400px"
    upload-dir="editor"
  />
</template>
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modelValue | string | '' | 编辑器内容（支持 v-model） |
| height | string | '300px' | 编辑器高度 |
| placeholder | string | '请输入内容...' | 占位符 |
| disabled | boolean | false | 是否禁用 |
| mode | 'default' \| 'simple' | 'default' | 编辑器模式 |
| uploadDir | string | 'editor' | 图片上传目录 |
| toolbarConfig | Partial<IToolbarConfig> | {} | 工具栏配置 |
| editorConfig | Partial<IEditorConfig> | {} | 编辑器配置 |

## 组件事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | (value: string) | 内容变化时触发 |
| change | (value: string) | 内容变化时触发 |

## 组件方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getHtml | - | string | 获取 HTML 内容 |
| getText | - | string | 获取纯文本内容 |
| setHtml | (html: string) | void | 设置 HTML 内容 |
| insertText | (text: string) | void | 插入文本 |
| focus | - | void | 聚焦编辑器 |
| blur | - | void | 失焦编辑器 |

## 使用示例

### 1. 基础使用

```vue
<template>
  <WangEditor
    v-model="content"
    placeholder="请输入内容..."
    height="400px"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import WangEditor from '@/components/Editor/index.vue'

const content = ref('')
</script>
```

### 2. 表单集成

```vue
<template>
  <el-form :model="form" :rules="rules" ref="formRef">
    <el-form-item label="内容" prop="content">
      <WangEditor
        v-model="form.content"
        placeholder="请输入内容..."
        height="400px"
        upload-dir="form"
      />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { type FormInstance } from 'element-plus'
import WangEditor from '@/components/Editor/index.vue'

const formRef = ref<FormInstance>()
const form = reactive({
  content: ''
})

const rules = {
  content: [
    { required: true, message: '请输入内容', trigger: 'blur' }
  ]
}
</script>
```

### 3. 简单模式

```vue
<template>
  <WangEditor
    v-model="content"
    mode="simple"
    height="200px"
    placeholder="简单模式编辑器..."
  />
</template>
```

### 4. 禁用状态

```vue
<template>
  <WangEditor
    v-model="content"
    :disabled="true"
    height="300px"
  />
</template>
```

### 5. 自定义配置

```vue
<template>
  <WangEditor
    v-model="content"
    :toolbar-config="toolbarConfig"
    :editor-config="editorConfig"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { IToolbarConfig, IEditorConfig } from '@wangeditor/editor'

const toolbarConfig: Partial<IToolbarConfig> = {
  excludeKeys: ['emotion', 'insertVideo']
}

const editorConfig: Partial<IEditorConfig> = {
  placeholder: '自定义占位符...'
}
</script>
```

## 在业务表单中的应用

已在 `src/views/bizagreement/bizAgreement/components/BizAgreementForm.vue` 中集成：

```vue
<el-form-item label="内容" prop="agreementContent">
  <WangEditor
    v-model="paramsProps.row.agreementContent"
    placeholder="请填写协议内容..."
    height="400px"
    upload-dir="agreement"
  />
</el-form-item>
```

## 图片上传

组件已集成项目现有的图片上传 API (`uploadFile`)，支持：
- 自动上传到指定目录
- 上传进度提示
- 错误处理
- 图片预览

## 注意事项

1. 确保已正确导入 wangEditor 样式文件
2. 图片上传需要后端 API 支持
3. 在表单中使用时，确保正确配置验证规则
4. 组件销毁时会自动清理编辑器实例

## 测试页面

访问 `/test-editor` 或 `/minimal-editor-test` 查看组件演示。
