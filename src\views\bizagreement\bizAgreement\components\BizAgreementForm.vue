<template>
  <el-dialog
    v-model="visible"
    :title="`${paramsProps.title}`"
    :destroy-on-close="true"
    width="900px"
    draggable
  >
    <el-form
      ref="ruleFormRef"
      label-width="140px"
      label-suffix=" :"
      :rules="rules"
      :model="paramsProps.row"
      @submit.enter.prevent="handleSubmit"
    >
      <el-form-item label="业务类型" prop="agreementType">
        <el-select v-model="paramsProps.row.agreementType" clearable placeholder="请选择业务类型">
          <el-option
            v-for="item in useDictOptions('agreement_type').value"
            :key="item.id"
            :label="item.codeName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    <el-form-item label="标题" prop="title">
        <el-input
          v-model="paramsProps.row.title"
          placeholder="请填写标题"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="内容" prop="agreementContent">
        <WangEditor
          v-model="paramsProps.row.agreementContent"
          placeholder="请填写协议内容..."
          height="400px"
          upload-dir="agreement"
        />
      </el-form-item>
      <el-form-item label="启用" prop="isNot">
        <el-radio-group v-model="paramsProps.row.isNot">
          <el-radio-button v-for="item in useDictOptions('is_not').value" :key="item.id" :value="item.id">
            {{ item.codeName }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="paramsProps.row.remark"
          placeholder="请填写备注"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="sortNum">
        <el-input-number
          v-model="paramsProps.row.sortNum" :precision="0" :min="1" :max="999999" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false"> 取消</el-button>
      <el-button type="primary" @click="handleSubmit"> 确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { type ElForm, ElMessage } from 'element-plus'
import { useDictOptions } from '@/hooks/useDictOptions'
import WangEditor from '@/components/Editor/index.vue'

defineOptions({
    name: 'BizAgreementForm'
})

const rules = reactive({
  agreementContent: [
    { required: true, message: '请填写协议内容', trigger: 'blur' }
  ]
})

const visible = ref(false)
const paramsProps = ref<View.DefaultParams>({
  title: '',
  row: {},
  api: undefined,
  getTableList: undefined
})

// 接收父组件传过来的参数
const acceptParams = (params: View.DefaultParams) => {
  paramsProps.value = params
  visible.value = true
}

// 提交数据（新增/编辑）
const ruleFormRef = ref<InstanceType<typeof ElForm>>()
const handleSubmit = () => {
  ruleFormRef.value!.validate(async (valid) => {
    if (!valid) return
    try {
      await paramsProps.value.api!(paramsProps.value.row)
      ElMessage.success({ message: `${paramsProps.value.title}成功！` })
      paramsProps.value.getTableList!()
      visible.value = false
    } catch (error) {
      console.log(error)
    }
  })
}

defineExpose({
  acceptParams
})
</script>

<style scoped lang="scss"></style>