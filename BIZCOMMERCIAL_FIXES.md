# BizCommercial 组件修复说明

## 问题描述
1. `handlePreview` 函数无法正确显示图片预览
2. 编辑模式下文件组件无法回显已有的文件信息
3. 缺少图片预览组件

## 解决方案

### 1. 更新类型定义 (`src/api/types/bizcommercial/bizCommercial.ts`)

**修改前：**
```typescript
export type BizCommercialRow = {
    fileId?: string
    createTime?: string
    updateTime?: string
    sortNum?: number
    remark?: string
}
```

**修改后：**
```typescript
export type BizCommercialRow = {
    commercialId?: number
    fileId?: string
    filename?: string
    url?: string
    createTime?: string
    updateTime?: string
    sortNum?: number
    remark?: string
}
```

### 2. 修复图片预览功能 (`src/views/bizcommercial/bizCommercial/index.vue`)

#### 添加图片预览组件
```vue
<!-- 图片预览 -->
<el-image-viewer
  v-if="previewVisible"
  :url-list="[previewImageUrl]"
  @close="previewVisible = false"
/>
```

#### 修复 handlePreview 函数
```typescript
const handlePreview = (row: BizCommercialRow) => {
  if (row.url) {
    previewImageUrl.value = row.url;
    previewVisible.value = true;
  } else {
    // 如果没有 url，可以根据 fileId 构建 URL
    if (row.fileId) {
      previewImageUrl.value = `/api/admin/sys-file/getFile?fileId=${row.fileId}`;
      previewVisible.value = true;
    }
  }
};
```

### 3. 修复编辑模式文件回显 (`src/views/bizcommercial/bizCommercial/components/BizCommercialForm.vue`)

#### 修改 acceptParams 方法
```typescript
const acceptParams = (params: View.DefaultParams) => {
  paramsProps.value = params
  visible.value = true

  // 初始化文件列表，编辑模式下回显已有文件
  if (params.row?.fileId && params.row?.filename && params.row?.url) {
    fileList.value = [{
      name: params.row.filename,
      url: params.row.url,
      status: 'success',
      uid: Date.now()
    }]
  } else {
    fileList.value = []
  }
}
```

#### 优化文件变化处理
```typescript
const fileChange = (file: INewUploadResult) => {
  if (file) {
    // 新上传的文件
    paramsProps.value.row.fileId = file.id
  } else {
    // 文件被删除，如果是编辑模式且原本有文件，则清空 fileId
    if (fileList.value.length === 0) {
      paramsProps.value.row.fileId = null
    }
    // 如果 fileList 中还有文件（编辑模式下的原有文件），保持原有 fileId
  }
}
```

## 功能特性

### 图片预览
- 点击"预览"按钮可以查看图片
- 支持两种 URL 获取方式：
  1. 直接使用 `row.url`（如果服务器返回了完整 URL）
  2. 根据 `row.fileId` 构建 URL：`/api/admin/sys-file/getFile?fileId=${fileId}`

### 文件回显
- 编辑模式下会自动显示已有的文件
- 支持下载和预览已有文件
- 可以删除原有文件并重新上传
- 如果不重新上传，保持原有的 fileId

### 文件上传
- 支持图片格式：jpg、jpeg、png、gif、bmp、webp
- 文件大小限制：5MB
- 单文件上传

## 使用说明

1. **新增模式**：直接上传文件即可
2. **编辑模式**：
   - 会自动显示已有文件（如果有）
   - 可以保留原文件或重新上传
   - 点击预览按钮查看图片
3. **预览功能**：点击操作列的"预览"按钮查看图片

## 注意事项

1. 服务器需要返回包含 `commercialId`、`filename`、`url` 字段的数据
2. 文件访问 URL 格式：`/api/admin/sys-file/getFile?fileId=${fileId}`
3. 确保服务器支持通过 fileId 获取文件的接口
