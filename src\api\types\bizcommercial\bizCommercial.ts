import type { IPageQuery } from '@/api/types'

// 查询条件
export type BizCommercialQuery = IPageQuery & {
    createTime?: string
  }

// 编辑form表单
export type BizCommercialForm = {
    fileId?: string
    sortNum?: number
    remark?: string
 }

// list或detail返回结构
export type BizCommercialRow = {
    commercialId?: number
    fileId?: string
    filename?: string
    url?: string
    createTime?: string
    updateTime?: string
    sortNum?: number
    remark?: string
  }

