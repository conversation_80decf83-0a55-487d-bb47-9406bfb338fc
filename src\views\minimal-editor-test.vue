<template>
  <div style="padding: 20px;">
    <h2>最小化编辑器测试</h2>
    
    <div style="margin: 20px 0;">
      <div ref="toolbarRef" style="border: 1px solid #ccc; border-bottom: none;"></div>
      <div ref="editorRef" style="border: 1px solid #ccc; height: 300px;"></div>
    </div>
    
    <div>
      <el-button @click="getContent">获取内容</el-button>
      <el-button @click="setContent">设置内容</el-button>
    </div>
    
    <div style="margin-top: 20px;">
      <h3>当前内容：</h3>
      <pre>{{ content }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { createEditor, createToolbar } from '@wangeditor/editor'
import type { IDomEditor } from '@wangeditor/editor'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'MinimalEditorTest'
})

const editorRef = ref<HTMLElement>()
const toolbarRef = ref<HTMLElement>()
const content = ref('')
let editor: IDomEditor | null = null

const initEditor = () => {
  if (!editorRef.value || !toolbarRef.value) {
    console.warn('容器未准备好')
    return
  }

  try {
    console.log('开始初始化编辑器...')
    
    // 创建编辑器
    editor = createEditor({
      selector: editorRef.value,
      config: {
        placeholder: '请输入内容...'
      }
    })

    // 创建工具栏
    createToolbar({
      editor,
      selector: toolbarRef.value
    })

    // 监听内容变化
    editor.on('change', () => {
      content.value = editor?.getHtml() || ''
    })

    console.log('编辑器初始化成功')
    ElMessage.success('编辑器初始化成功')
  } catch (error) {
    console.error('编辑器初始化失败:', error)
    ElMessage.error('编辑器初始化失败: ' + error)
  }
}

const getContent = () => {
  if (editor) {
    const html = editor.getHtml()
    content.value = html
    ElMessage.info('内容已获取')
    console.log('当前内容:', html)
  }
}

const setContent = () => {
  if (editor) {
    const testContent = '<p>这是测试内容</p><p><strong>加粗文本</strong></p>'
    editor.setHtml(testContent)
    content.value = testContent
    ElMessage.success('内容已设置')
  }
}

onMounted(() => {
  console.log('组件已挂载')
  nextTick(() => {
    console.log('nextTick 执行')
    setTimeout(() => {
      console.log('延迟执行初始化')
      initEditor()
    }, 200)
  })
})

onBeforeUnmount(() => {
  if (editor) {
    editor.destroy()
    editor = null
  }
})
</script>
